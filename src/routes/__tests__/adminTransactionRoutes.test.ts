import { clearDb, closeDb, connectDb } from "../../tests/utils/db";
import request from "supertest";
import supertest from "supertest";
import app from "../../app";
import {
  buildAssetTransaction,
  buildBankAccount,
  buildChargeTransaction,
  buildDepositCashTransaction,
  buildDividendTransaction,
  buildHoldingDTO,
  buildIntraDayPortfolioTicker,
  buildInvestmentProduct,
  buildOrder,
  buildPortfolio,
  buildRebalanceTransaction,
  buildReward,
  buildSavingsTopup,
  buildSubscription,
  buildUser,
  buildWithdrawalCashTransaction
} from "../../tests/utils/generateModels";
import {
  AssetTransaction,
  AssetTransactionDocument,
  DepositCashTransaction,
  DepositCashTransactionDocument,
  DividendTransaction,
  DividendTransactionDocument,
  RebalanceTransaction,
  RebalanceTransactionDocument,
  SavingsTopupTransaction,
  SavingsTopupTransactionDocument,
  Transaction,
  TransactionDocument,
  WithdrawalCashTransaction
} from "../../models/Transaction";
import { KycStatusEnum, User, UserDocument } from "../../models/User";
import { Portfolio, PortfolioDocument, PortfolioModeEnum } from "../../models/Portfolio";
import {
  buildWealthkernelOrderResponse,
  buildWealthkernelTransactionResponse
} from "../../tests/utils/generateWealthkernel";
import { CurrencyEnum, TransactionType, WealthkernelService } from "../../external-services/wealthkernelService";
import { faker } from "@faker-js/faker";
import { Order, OrderDocument, OrderDTOInterface, OrderSubmissionIntentEnum } from "../../models/Order";
import eventEmitter from "../../loaders/eventEmitter";
import Decimal from "decimal.js";
import events from "../../event-handlers/events";
import { buildPaymentType, buildPaymentTypeResults } from "../../tests/utils/generateTruelayer";
import { PaymentStatusTypeV3, TruelayerPaymentsClient } from "../../external-services/truelayerService";
import logger from "../../external-services/loggerService";
import OrderService from "../../services/orderService";
import { fees, investmentUniverseConfig, savingsUniverseConfig } from "@wealthyhood/shared-configs";
import { ProviderEnum } from "../../configs/providersConfig";
import { RedisClientService } from "../../loaders/redis";
import { DepositActionEnum } from "../../configs/depositsConfig";
import DateUtil from "../../utils/dateUtil";
import { FX_FEE_SPREADS_WH } from "../../configs/fxSpreadsConfig";

const { ASSET_CONFIG } = investmentUniverseConfig;
const { MINIMUM_FX_FEE, MINIMUM_COMMISSION_FEE } = fees;

const ZERO_GBP_FEES = {
  fx: { currency: CurrencyEnum.GBP, amount: 0 },
  commission: { currency: CurrencyEnum.GBP, amount: 0 },
  executionSpread: { currency: CurrencyEnum.GBP, amount: 0 },
  realtimeExecution: { currency: CurrencyEnum.GBP, amount: 0 }
};

describe("AdminTransactionRoutes", () => {
  beforeAll(async () => await connectDb("AdminTransactionRoutes"));
  afterAll(async () => await closeDb());

  describe("/transactions", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions (with query param sort) should return status 200 with transactions array", async () => {
      await Promise.all([buildAssetTransaction(), buildDepositCashTransaction()]);

      const response = await request(app)
        .get("/api/admin/m2m/transactions?sort=-createdAt")
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({}).sort({ createdAt: -1 });

      const transactionsReceived: TransactionDocument[] = JSON.parse(response.text);
      expect(transactionsReceived).toMatchObject(JSON.parse(JSON.stringify(expectedData)));

      const isArraySorted = (arr: any[]) => arr.slice(1).every((item, i) => arr[i] >= item); //desc order
      expect(isArraySorted(transactionsReceived.map((transaction) => new Date(transaction.createdAt)))).toEqual(
        true
      );
    });

    it("GET /transactions (with query param owner) should return status 200 with transactions array", async () => {
      const user = await buildUser();
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get(`/api/admin/m2m/transactions?owner=${user.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ owner: user.id });

      expect(expectedData.length).toEqual(2);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });

    it("GET /transactions (with query param portfolio) should return status 200 with transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      const [singleAssetTransaction] = await Promise.all([
        buildAssetTransaction({
          owner: user.id,
          portfolio,
          portfolioTransactionCategory: "update"
        }),
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildDepositCashTransaction({ owner: user.id, portfolio }),
        buildAssetTransaction(),
        buildAssetTransaction(),
        buildInvestmentProduct(true, { assetId: "equities_uk" })
      ]);

      singleAssetTransaction.orders = [
        await buildOrder({
          status: "Pending",
          side: "Buy",
          transaction: singleAssetTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin
        })
      ];
      await singleAssetTransaction.save();

      const response = await request(app)
        .get(`/api/admin/m2m/transactions?portfolio=${portfolio.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ portfolio: portfolio.id });

      expect(expectedData.length).toEqual(3);
      const responseData = JSON.parse(response.text);
      expect(responseData).toEqual(
        expect.arrayContaining([
          expect.objectContaining({
            id: expectedData[0].id
          }),
          expect.objectContaining({
            id: expectedData[1].id
          }),
          expect.objectContaining({
            id: expectedData[2].id
          })
        ])
      );
    });

    it("GET /transactions (with query params portfolio, owner) should return status 200 with transactions array", async () => {
      const user = await buildUser();
      const portfolio = await buildPortfolio({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id, portfolio }),
        buildDepositCashTransaction({ owner: user.id }),
        buildAssetTransaction(),
        buildAssetTransaction()
      ]);

      const response = await request(app)
        .get(`/api/admin/m2m/transactions?portfolio=${portfolio.id}&owner=${user.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedData = await Transaction.find({ owner: user.id, portfolio: portfolio.id });

      expect(expectedData.length).toEqual(1);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedData)));
    });
  });

  describe("/transactions/assets", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/assets (without required 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'pageSize' is required" }
        })
      );
    });

    it("GET /transactions/assets (without required 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?pageSize=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'page' is required" }
        })
      );
    });

    it("GET /transactions/assets (with not numeric 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?pageSize=1&page=blah")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/assets (with not numeric 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?pageSize=blah&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/assets?populateOwner=true should return status 200 with pagination info and transactions array", async () => {
      const user = await buildUser();
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildAssetTransaction({ owner: user.id }),
        buildAssetTransaction({ owner: user.id }),
        buildAssetTransaction({ owner: user.id }),
        buildAssetTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?populateOwner=true&pageSize=2&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedTransactions = (await AssetTransaction.find({}).populate("owner")).slice(0, 2);
      expect(JSON.parse(response.text)).toMatchObject(
        JSON.parse(
          JSON.stringify({
            pagination: {
              page: 1,
              pageSize: 2,
              pages: 3 // 5 transactions with page size 2 so 5 / 2 = 2.5 ceil to nearest integer 3
            },
            transactions: expectedTransactions
          })
        )
      );
    });

    it("GET /transactions/assets?populateOwner=true should return status 200 with pagination info and transactions array with populated single orders for portfolio updates", async () => {
      const user = await buildUser();

      const singleOrderTransaction = await buildAssetTransaction({
        owner: user.id,
        portfolioTransactionCategory: "update"
      });
      singleOrderTransaction.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Sell",
          transaction: singleOrderTransaction.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
          quantity: 1
        })
      ];
      await singleOrderTransaction.save();

      await buildInvestmentProduct(true, { assetId: "equities_us" });
      const singleOrderTransactionPortfolioBuy = await buildAssetTransaction({
        owner: user.id,
        portfolioTransactionCategory: "buy"
      });
      singleOrderTransactionPortfolioBuy.orders = [
        await buildOrder({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending", submittedAt: new Date() } },
          side: "Buy",
          transaction: singleOrderTransactionPortfolioBuy.id,
          isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin
        })
      ];
      await singleOrderTransactionPortfolioBuy.save();

      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets?populateOwner=true&pageSize=2&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedTransactions: AssetTransactionDocument[] = (
        await AssetTransaction.find({}).populate("owner")
      ).slice(0, 2);

      for (const transaction of expectedTransactions) {
        if (transaction.orders.length == 1 && transaction.portfolioTransactionCategory == "update") {
          await transaction.populate("orders");
        }
      }

      expect(JSON.parse(response.text)).toMatchObject(
        JSON.parse(
          JSON.stringify({
            pagination: {
              page: 1,
              pageSize: 2,
              pages: 1
            },
            transactions: expectedTransactions
          })
        )
      );
    });
  });

  describe("/transactions/deposits", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/deposits (without required 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits?page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'pageSize' is required" }
        })
      );
    });

    it("GET /transactions/deposits (without required 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits?pageSize=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'page' is required" }
        })
      );
    });

    it("GET /transactions/deposits (with not numeric 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits?pageSize=1&page=blah")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/deposits (with not numeric 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits?pageSize=blah&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/deposits?populateOwner=true should return status 200 with pagination info and transactions array", async () => {
      const user = await buildUser();
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id }),
        buildDepositCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits?populateOwner=true&pageSize=2&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedTransactions = (await DepositCashTransaction.find({}).populate("owner")).slice(0, 2);
      expect(JSON.parse(response.text)).toMatchObject(
        JSON.parse(
          JSON.stringify({
            pagination: {
              page: 1,
              pageSize: 2,
              pages: 3 // 5 transactions with page size 2 so 5 / 2 = 2.5 ceil to nearest integer 3
            },
            transactions: expectedTransactions
          })
        )
      );
    });
  });

  describe("/transactions/withdrawals", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/withdrawals (without required 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/withdrawals?page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'pageSize' is required" }
        })
      );
    });

    it("GET /transactions/withdrawals (without required 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/withdrawals?pageSize=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: { description: "Invalid parameter", message: "Param 'page' is required" }
        })
      );
    });

    it("GET /transactions/withdrawals (with not numeric 'page' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/withdrawals?pageSize=1&page=blah")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'page' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/withdrawals (with not numeric 'pageSize' query param) should return status 400 with proper cause", async () => {
      const response = await request(app)
        .get("/api/admin/m2m/transactions/withdrawals?pageSize=blah&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'pageSize' , should be numeric"
          }
        })
      );
    });

    it("GET /transactions/withdrawals?populateOwner=true should return status 200 with pagination info and transactions array", async () => {
      const user = await buildUser();
      await Promise.all([
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get("/api/admin/m2m/transactions/withdrawals?populateOwner=true&pageSize=2&page=1")
        .set("Accept", "application/json");

      expect(response.status).toEqual(200);
      const expectedTransactions = (await WithdrawalCashTransaction.find({}).populate("owner")).slice(0, 2);
      expect(JSON.parse(response.text)).toMatchObject(
        JSON.parse(
          JSON.stringify({
            pagination: {
              page: 1,
              pageSize: 2,
              pages: 3 // 5 transactions with page size 2 so 5 / 2 = 2.5 ceil to nearest integer 3
            },
            transactions: expectedTransactions
          })
        )
      );
    });
  });

  describe("/transactions/:id", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/:id (with invalid id) should return status 400 with proper cause", async () => {
      await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/transactions/sdasda243%$")
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("GET /transactions/:id (with invalid id) should return status 400 with proper cause", async () => {
      await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/transactions/foo123")
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'id'"
          }
        })
      );
    });

    it("GET /transactions/:id (with valid id) should return status 200 with transaction object", async () => {
      const user = await buildUser();
      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/admin/m2m/transactions/${transaction.id}?populateOrders=true`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await Transaction.findById(transaction.id);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
      expect(JSON.parse(response.text).displayAmount).not.toBeUndefined();
    });
  });

  describe("/transactions/deposits/:id", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/deposits/:id (with invalid id) should return status 400 with proper cause", async () => {
      await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits/sdasda243%$")
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("GET /transactions/deposits/:id (with invalid id) should return status 400 with proper cause", async () => {
      await buildUser();
      const response = await request(app)
        .get("/api/admin/m2m/transactions/deposits/foo123")
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'id'"
          }
        })
      );
    });

    it("GET /transactions/deposits/:id (with valid id) should return status 200 with transaction object", async () => {
      const user = await buildUser();
      const transaction = await buildDepositCashTransaction({ owner: user.id });
      await Promise.all([
        buildAssetTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/admin/m2m/transactions/deposits/${transaction.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await Transaction.findById(transaction.id);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });
  });

  describe("/transactions/assets/:id", () => {
    beforeEach(() => jest.clearAllMocks());
    afterEach(async () => await clearDb());

    it("GET /transactions/assets/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets/sdasda243%$")
        .set("external-user-id", user.id)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid url",
            message: "Invalid url"
          }
        })
      );
    });

    it("GET /transactions/assets/:id (with invalid id) should return status 400 with proper cause", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const response = await request(app)
        .get("/api/admin/m2m/transactions/assets/foo123")
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for 'id'"
          }
        })
      );
    });

    it("GET /transactions/assets/:id?populateOrders=foo (with valid populateOrders param) should return status 200 with transaction object with unpopulated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const transaction = await buildAssetTransaction({ owner: user.id });
      const response = await request(app)
        .get(`/api/admin/m2m/transactions/assets/${transaction.id}?populateOrders=foo`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(400);
      expect(JSON.parse(response.text)).toMatchObject(
        expect.objectContaining({
          error: {
            description: "Invalid parameter",
            message: "Invalid value for param 'populateOrders' , should be boolean"
          }
        })
      );
    });

    it("GET /transactions/assets/:id (with valid id) should return status 200 with transaction object with unpopulated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/admin/m2m/transactions/assets/${transaction.id}`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await Transaction.findById(transaction.id);
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });

    it("GET /transactions/assets/:id?populateOrders=true (with valid id and populateOrdes param) should return status 200 with transaction object with unpopulated orders", async () => {
      const user = await buildUser();
      await buildSubscription({ owner: user.id });
      const transaction = await buildAssetTransaction({ owner: user.id });
      await Promise.all([
        buildDepositCashTransaction({ owner: user.id }),
        buildWithdrawalCashTransaction({ owner: user.id })
      ]);
      const response = await request(app)
        .get(`/api/admin/m2m/transactions/assets/${transaction.id}?populateOrders=true`)
        .set("Accept", "application/json");
      expect(response.status).toEqual(200);
      const expectedTransaction = await Transaction.findById(transaction.id).populate("orders");
      expect(JSON.parse(response.text)).toMatchObject(JSON.parse(JSON.stringify(expectedTransaction)));
    });
  });

  describe("/transactions/rebalances/process", () => {
    beforeEach(async () => {
      jest.resetAllMocks();
      // Current FX rate GBP <-> USD is 5
      const FX_RATES = {
        USD: {
          USD: 1,
          EUR: 0.2,
          GBP: 0.2
        },
        EUR: {
          EUR: 1,
          GBP: 1,
          USD: 5
        },
        GBP: {
          GBP: 1,
          EUR: 1,
          USD: 5
        }
      };
      await RedisClientService.Instance.set("fxRates", FX_RATES);
    });
    afterEach(async () => await clearDb());

    describe("when there are no rebalance transactions", () => {
      it("should return a 204", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);
      });
    });

    describe("when there is a rebalance transaction but its rebalance status is 'Cancelled'", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "Cancelled"
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not process the rebalance transaction", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Cancelled");
      });
    });

    describe("when there is a rebalance transaction but its owner also has an asset transaction pending", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: []
        });

        await buildAssetTransaction({ portfolio: portfolio, owner: user, status: "Pending" });

        rebalanceTransaction = await buildRebalanceTransaction({ portfolio: portfolio, owner: user });
      });

      it("should return a 204 and not process the rebalance transaction", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("NotStarted");
      });
    });

    describe("when there is a rebalance transaction but its owner also has an asset transaction pending deposit with an executed payment status", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        const depositTransaction: DepositCashTransactionDocument = await buildDepositCashTransaction(
          {
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              }
            }
          },
          user,
          portfolio
        );
        await buildAssetTransaction({
          portfolio: portfolio,
          owner: user,
          status: "PendingDeposit",
          pendingDeposit: depositTransaction._id
        });

        rebalanceTransaction = await buildRebalanceTransaction({ portfolio: portfolio, owner: user });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not process the rebalance transaction", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("NotStarted");
      });
    });

    describe("when there is a rebalance transaction but its owner also has an asset transaction pending deposit with a cancelled payment status", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        const depositTransaction: DepositCashTransactionDocument = await buildDepositCashTransaction(
          {
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "failed"
              }
            }
          },
          user,
          portfolio
        );
        await buildAssetTransaction({
          portfolio: portfolio,
          owner: user,
          status: "PendingDeposit",
          pendingDeposit: depositTransaction._id
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and move the rebalance transaction to PendingSell", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");
      });
    });

    describe("when there is a rebalance transaction but its owner also has a pending charge transaction (holdings) with unmatched orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        const subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });

        const chargeTransaction = await buildChargeTransaction({
          portfolio: portfolio,
          owner: user,
          status: "Pending",
          chargeMethod: "holdings",
          subscription: subscription
        });

        await buildOrder({
          transaction: chargeTransaction._id,
          isin: ASSET_CONFIG["equities_global"].isin,
          quantity: 1,
          side: "Sell",
          providers: { wealthkernel: { status: "Pending", id: "real_estate_us", submittedAt: new Date() } }
        });

        rebalanceTransaction = await buildRebalanceTransaction({ portfolio: portfolio, owner: user });
      });

      it("should return a 204 and not process the rebalance transaction", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("NotStarted");
      });
    });

    describe("when there is a rebalance transaction but its owner also has an pending charge transaction (holdings) and all its orders are matched", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        const subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });

        const chargeTransaction = await buildChargeTransaction({
          portfolio: portfolio,
          owner: user,
          status: "Pending",
          chargeMethod: "cash",
          subscription: subscription
        });

        await buildOrder({
          transaction: chargeTransaction._id,
          isin: ASSET_CONFIG["equities_global"].isin,
          quantity: 1,
          side: "Sell",
          providers: { wealthkernel: { status: "Matched", id: "real_estate_us", submittedAt: new Date() } }
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and move the rebalance transaction to PendingSell", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");
      });
    });

    describe("when there is a rebalance transaction but its owner also has an pending charge transaction (cash)", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        const subscription = await buildSubscription({
          category: "FeeBasedSubscription",
          active: true,
          price: "free_monthly",
          owner: user.id
        });

        await buildChargeTransaction({
          portfolio: portfolio,
          owner: user,
          status: "Pending",
          chargeMethod: "cash",
          subscription: subscription
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and move the rebalance transaction to PendingSell", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status and a sell order under our asset quantity limit", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const userRequestingVerySmallUpdate = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          portfolioConversionStatus: "completed"
        });

        // Current percentage for equities_global_industrials_broad is 10%, and our target allocation is 9%.
        // However, for this to decrease by 1%, we need to make a sell order under 0.0001 which is not allowed.
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_global_health_care_broad", quantity: 1, price: 30, percentage: 41 },
          { assetId: "equities_global_health_innovation", quantity: 1, price: 60, percentage: 50 },
          { assetId: "equities_global_industrials_broad", quantity: 0.0002, price: 50000, percentage: 9 }
        ];

        const portfolio = await buildPortfolio({
          owner: userRequestingVerySmallUpdate.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: userRequestingVerySmallUpdate,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not create that order", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders = await Order.find({ transaction: rebalanceTransaction._id });

        expect(orders).toHaveLength(1);
        expect(orders[0]).toEqual(
          expect.objectContaining({
            isin: ASSET_CONFIG["equities_global_health_innovation"].isin,
            quantity: 0.1667
          })
        );
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status and a sell order under our asset amount limit", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const userRequestingVerySmallUpdate = await buildUser({
          kycStatus: KycStatusEnum.PASSED,
          portfolioConversionStatus: "completed"
        });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          percentage: number;
          price: number;
        }[] = [
          { assetId: "equities_uk", quantity: 1, percentage: 50, price: 4.999 },
          { assetId: "equities_us", quantity: 1, percentage: 50, price: 5.001 }
        ];

        const portfolio = await buildPortfolio({
          owner: userRequestingVerySmallUpdate.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: userRequestingVerySmallUpdate,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not create that order", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders = await Order.find({ transaction: rebalanceTransaction._id });

        expect(orders).toHaveLength(0);
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status and an asset that will be rewarded (but is pending)", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        // The user has a reward which is not settled, which means we should ignore it.
        await buildReward({
          asset: "equities_global",
          referrer: user.id,
          referral: referral.id,
          targetUser: user.id,
          consideration: {
            currency: "GBP",
            amount: 2700,
            orderAmount: 2700,
            bonusAmount: 2700
          },
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Pending" } }
          }
        });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create the sell orders in our DB ignoring the pending reward", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders: OrderDocument[] = await Order.find({ transaction: rebalanceTransaction._id });
        expect(orders).toHaveLength(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              quantity: 0.5,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_global"].isin,
              quantity: 0.1667,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status and an asset that has been rewarded", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 100, percentage: 25 }, // Target is 0.25 of equities_global
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const referral = await buildUser({ kycStatus: KycStatusEnum.PASSED });

        // The user has two rewards for equities_global of 0.25 each, which means that from the single equities_global
        // asset have now, we are only allowed to sell 0.5.
        await buildReward({
          asset: "equities_global",
          referrer: user.id,
          referral: referral.id,
          targetUser: user.id,
          quantity: 0.25,
          status: "Settled",
          accepted: true,
          unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          }
        });
        await buildReward({
          asset: "equities_global",
          referrer: user.id,
          referral: referral.id,
          targetUser: user.id,
          quantity: 0.25,
          status: "Settled",
          accepted: true,
          unrestrictedAt: DateUtil.getDateOfDaysAgo(new Date(), -30),
          deposit: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Settled" } }
          },
          order: {
            activeProviders: [ProviderEnum.WEALTHKERNEL],
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Matched" } }
          }
        });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create the sell orders in our DB and change the rebalance status to PendingSell", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders: OrderDocument[] = await Order.find({ transaction: rebalanceTransaction._id });
        expect(orders).toHaveLength(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              quantity: 0.5,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            // As the target of equities_global is 0.25 and the portfolio currently has 1, we would normally sell 0.75. However, as 0.5 is reward, we can only sell 1 - 0.5 => 0.5.
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_global"].isin,
              quantity: 0.5,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10 },
          { assetId: "equities_global", quantity: 1, price: 30 },
          { assetId: "equities_uk", quantity: 1, price: 26 },
          { assetId: "equities_eu", quantity: 1, price: 14 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "NotStarted",
          targetAllocation: [
            {
              assetCommonId: "commodities_gold",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create the sell orders in our DB and change the rebalance status to PendingSell", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders: OrderDocument[] = await Order.find({ transaction: rebalanceTransaction._id });
        expect(orders).toHaveLength(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_uk"].isin,
              quantity: 1,
              side: "Sell",
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_global"].isin,
              quantity: 1,
              side: "Sell",
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when there is a rebalance transaction with a NotStarted status and the endpoint is hit twice", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 both times and only create sell orders once", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        const secondResponse = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);
        expect(secondResponse.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");

        const orders: OrderDocument[] = await Order.find({ transaction: rebalanceTransaction._id });
        expect(orders).toHaveLength(2);
        expect(orders).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              quantity: 0.5,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_global"].isin,
              quantity: 0.1667,
              side: "Sell",
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and there are sell orders without a WK ID", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell"
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell"
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not create buy orders", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        expect(logger.error).not.toHaveBeenCalled();

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(0);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and there are pending sell orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveOrder")
          .mockResolvedValue(buildWealthkernelOrderResponse({ status: "Pending", side: "Sell" }));

        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not create buy orders", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(0);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingSell");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and there are no sell orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveOrder")
          .mockResolvedValue(buildWealthkernelOrderResponse({ status: "Pending", side: "Sell" }));

        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: { amount: 0, currency: "GBP" },
              commission: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(0);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Settled");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and all sell orders are rejected", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, not create buy orders and set status to Rejected", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(0);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Rejected");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and one of the assets was sold in previous stage", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        // Because of price fluctuations, equities_eu is now less than £10 even though in the first stage
        // it was sold because then it was over our target holding. We should not create a buy order for it.
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "equities_uk", quantity: 1, price: 10, percentage: 51 },
          { assetId: "equities_eu", quantity: 1, price: 9, percentage: 49 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            quantity: 0.2,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: 100
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 20 }
        });
      });

      it("should return a 204 and not create a buy order for that one asset", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });

        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_uk"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: 100
              }),
              side: "Buy"
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, all sell orders are matched and the target allocation contains an asset which we do not hold currently", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 0, price: 10 }, // Current 0, target 50%
          { assetId: "equities_eu", quantity: 1, price: 30 }, // Current 30%, target 50%
          { assetId: "equities_global_ai", quantity: 1, price: 50 }, // Current 50%, target 0%
          { assetId: "equities_uk", quantity: 1, price: 20 } // Current 20%, target 0%
        ];

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, settled: 10, reserved: 10 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: (
            await Promise.all(
              ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
                buildHoldingDTO(true, assetId, quantity, { price })
              )
            )
          ).filter((config) => config.quantity > 0)
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "commodities_gold",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price * 100
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_uk", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global_ai"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_global_ai").price * 100
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_global_ai", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: { amount: 0, currency: "GBP" }, // There are no USD traded orders
              commission: { amount: 0, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: 5000,
                originalAmount: 5000
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: 2000,
                originalAmount: 2000
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, all sell orders are matched and there are no USD traded assets in transaction", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10 }, // Current 30%, target 50%
          { assetId: "equities_eu", quantity: 1, price: 14 }, // Current 14%, target 50%
          { assetId: "equities_global_ai", quantity: 1, price: 30 }, // Current 30%, target 0%
          { assetId: "equities_uk", quantity: 1, price: 26 } // Current 26%, target 0%
        ];

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "commodities_gold",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount: ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_uk", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global_ai"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount:
                ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_global_ai").price * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_global_ai", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction.toObject()).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: { amount: 0, currency: "GBP" }, // There are no USD traded orders
              commission: { amount: 0, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              consideration: expect.objectContaining({
                amount: 2000,
                currency: "GBP",
                originalAmount: 2000
              }),
              side: "Buy",
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: 3600,
                currency: "GBP",
                originalAmount: 3600
              }),
              side: "Buy",
              activeProviders: [ProviderEnum.WEALTHKERNEL],
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, all sell orders are matched and there are no USD traded assets in the sell orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "real_estate_us", quantity: 3, price: 10 }, // Current 30%, target 50%
          { assetId: "equities_eu", quantity: 1, price: 14 }, // Current 14%, target 50%
          { assetId: "equities_global_ai", quantity: 1, price: 30 }, // Current 30%, target 0%
          { assetId: "equities_uk", quantity: 1, price: 26 } // Current 26%, target 0%
        ];

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "real_estate_us",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount: ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_uk", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global_ai"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount:
                ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_global_ai").price * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_global_ai", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction.toObject()).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: { amount: new Decimal(20).mul(FX_FEE_SPREADS_WH.free).toNumber(), currency: "GBP" }, // 15 bps of £20 (USD traded asset order)
              commission: { amount: 0, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["real_estate_us"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: 1997, // originalAmount (2000) - FX fee in pence (3) = 1997
                originalAmount: 2000 // £20 (original order)
              }),
              side: "Buy",
              fees: expect.objectContaining({
                fx: { amount: 0.03, currency: "GBP" },
                commission: { amount: 0, currency: "GBP" },
                executionSpread: { amount: 0, currency: "GBP" },
                realtimeExecution: { amount: 0, currency: "GBP" }
              }),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: 3600,
                currency: "GBP",
                originalAmount: 3600 // £36 (original order)
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, all sell orders are matched and there are no USD traded assets in the buy orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10 }, // Current 30%, target 50%
          { assetId: "equities_eu", quantity: 1, price: 14 }, // Current 14%, target 50%
          { assetId: "real_estate_us", quantity: 1, price: 30 }, // Current 30%, target 0%
          { assetId: "equities_uk", quantity: 1, price: 26 } // Current 26%, target 0%
        ];

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "commodities_gold",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              originalAmount:
                ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price * 100,
              amount:
                (ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price - 0.12) * 100,
              currency: "GBP"
            },
            fees: {
              fx: {
                amount: 0.12,
                currency: "GBP"
              },
              commission: {
                amount: 0,
                currency: "GBP"
              },
              executionSpread: {
                amount: 0,
                currency: "GBP"
              }
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_uk", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["real_estate_us"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              originalAmount:
                ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "real_estate_us").price * 100,
              amount:
                (ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "real_estate_us").price - 0.11) * 100,
              currency: "GBP"
            },
            fees: {
              fx: {
                amount: 0.11,
                currency: "GBP"
              },
              commission: {
                amount: 0,
                currency: "GBP"
              },
              executionSpread: {
                amount: 0,
                currency: "GBP"
              }
            },
            providers: {
              wealthkernel: { status: "Matched", id: "real_estate_us", submittedAt: new Date() }
            }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction.toObject()).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: { amount: new Decimal(0.12).plus(0.11).toDecimalPlaces(2).toNumber(), currency: "GBP" },
              commission: { amount: 0, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_gold"].isin,
              consideration: expect.objectContaining({
                amount: 1989,
                currency: "GBP",
                originalAmount: 1989
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: 3588,
                currency: "GBP",
                originalAmount: 3588
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, all sell orders are matched and there are USD traded assets in both sell and buy orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
        }[] = [
          { assetId: "commodities_silver", quantity: 3, price: 10 }, // Current 30%, target 50%
          { assetId: "equities_eu", quantity: 1, price: 14 }, // Current 14%, target 50%
          { assetId: "real_estate_us", quantity: 1, price: 30 }, // Current 30%, target 0%
          { assetId: "equities_uk", quantity: 1, price: 26 } // Current 26%, target 0%
        ];

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: [
            {
              assetCommonId: "commodities_silver",
              percentage: 50
            },
            {
              assetCommonId: "equities_eu",
              percentage: 50
            }
          ]
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount: ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "equities_uk").price * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "equities_uk", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["real_estate_us"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              originalAmount:
                ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "real_estate_us").price * 100,
              amount:
                (ASSET_COMMON_IDS_CONFIG.find((config) => config.assetId === "real_estate_us").price - 0.23) * 100,
              currency: "GBP"
            },
            fees: {
              fx: {
                amount: 0.23,
                currency: "GBP"
              },
              commission: {
                amount: 0,
                currency: "GBP"
              },
              executionSpread: {
                amount: 0,
                currency: "GBP"
              }
            },
            providers: {
              wealthkernel: { status: "Matched", id: "real_estate_us", submittedAt: new Date() }
            }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const updatedRebalanceTransaction = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalanceTransaction.toObject()).toEqual(
          expect.objectContaining({
            fees: expect.objectContaining({
              fx: {
                amount: new Decimal(0.23)
                  .plus(new Decimal(19.89).mul(FX_FEE_SPREADS_WH.free))
                  .toDecimalPlaces(2)
                  .toNumber(),
                currency: "GBP"
              }, // Pre-existing sell order FX fee (£0.23) + equities_global_financials_broad buy order FX fee (£19.89 × 0.0015 ≈ £0.03)
              commission: { amount: 0, currency: "GBP" },
              executionSpread: { amount: 0, currency: "GBP" }
            })
          })
        );

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["commodities_silver"].isin,
              consideration: expect.objectContaining({
                amount: 1986, // originalAmount (1989) - FX fee in pence (3) = 1986
                currency: "GBP",
                originalAmount: 1989 // £19.89 (original order)
              }),
              side: "Buy",
              fees: expect.objectContaining({
                fx: {
                  amount: new Decimal(19.95).mul(FX_FEE_SPREADS_WH.free).toDecimalPlaces(2).toNumber(),
                  currency: "GBP"
                }, // 15 bps of £19.95
                commission: { amount: 0, currency: "GBP" },
                executionSpread: { amount: 0, currency: "GBP" },
                realtimeExecution: { amount: 0, currency: "GBP" }
              }),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: 3588,
                currency: "GBP",
                originalAmount: 3588 // £35.88 (original order)
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and all sell orders are matched but add up to more than available cash when rounded", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 80, percentage: 0 },
          { assetId: "equities_global", quantity: 1, price: 10, percentage: 50 },
          { assetId: "equities_uk", quantity: 1, price: 10, percentage: 50 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount: 79.999 * 100,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, create buy orders and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });

        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              consideration: expect.objectContaining({
                amount: 4000,
                currency: "GBP",
                originalAmount: 4000
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              consideration: expect.objectContaining({
                amount: 3999,
                currency: "GBP",
                originalAmount: 3999
              }),
              side: "Buy",
              fees: expect.objectContaining(ZERO_GBP_FEES),
              isSubmittedToBroker: false,
              submissionIntent: OrderSubmissionIntentEnum.AGGREGATE
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and a buy order is under our asset investment amount limit", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 50, percentage: 0 },
          { assetId: "equities_global", quantity: 1, price: 49.99999, percentage: 50 },
          { assetId: "equities_uk", quantity: 1, price: 0.00001, percentage: 50 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, not create a buy order for the asset under our investment limit and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(1);
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_uk"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: expect.anything()
              }),
              side: "Buy"
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and a buy order is under our asset investment quantity limit", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 50, percentage: 0 },
          { assetId: "equities_global", quantity: 0.0001, price: 200000, percentage: 25 }, // We are going to try to buy £5 of this asset which is 0.000025 in quantity
          { assetId: "equities_uk", quantity: 1, price: 30, percentage: 75 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, not create a buy order for the asset under our investment limit and set status to PendingBuy", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(1);
        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_uk"].isin,
              consideration: expect.objectContaining({
                currency: "GBP",
                amount: expect.anything()
              }),
              side: "Buy"
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, and the last buy order is under our asset investment limit and there is a remainder amount left", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();
      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 50, percentage: 0 },
          { assetId: "equities_uk", quantity: 1, price: 30, percentage: 75 },
          { assetId: "equities_global", quantity: 0.0001, price: 200000, percentage: 25 } // We are going to try to buy £5 of this asset which is 0.000025 in quantity
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });
        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } },
            consideration: {
              currency: "GBP",
              amount: 5000
            }
          })
        ]);
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should be no remaining amount left", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        let amountOrderWasSold = 0;
        let amountOrderWasBought = 0;

        expect(buyOrdersBefore).toHaveLength(0);

        await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        const sellOrderAfterProcessing = await Order.find({ side: "Sell" });
        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterProcessing).toHaveLength(1);
        sellOrderAfterProcessing.forEach((sellOrder) => {
          amountOrderWasSold = sellOrder.consideration?.amount as number;
        });
        buyOrdersAfterProcessing.forEach((buyOrder) => {
          amountOrderWasBought = buyOrder.consideration?.originalAmount as number;
        });
        const remainingAmount = amountOrderWasSold - amountOrderWasBought;
        expect(remainingAmount).toEqual(0);
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, and the only buy order is under our asset investment limit and there is a remainder amount left", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();
      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 50, percentage: 0 },
          { assetId: "equities_global", quantity: 0.0001, price: 200000, percentage: 100 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });
        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              amount: 0.01,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should not process the rebalance transaction", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });

        expect(buyOrdersBefore).toHaveLength(0);

        await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        const updatedRebalance = await RebalanceTransaction.findById(rebalanceTransaction.id);
        expect(updatedRebalance.status).toBe("Pending");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status, with multiple orders and the last buy order is under our asset investment limit and there is a remainder amount left", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();
      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 1, price: 50, percentage: 0 },
          { assetId: "equities_uk", quantity: 1, price: 10, percentage: 15 },
          { assetId: "equities_eu", quantity: 1, price: 10, percentage: 25 },
          { assetId: "equities_us", quantity: 1, price: 10, percentage: 35 },
          { assetId: "equities_global", quantity: 0.0001, price: 200000, percentage: 25 } // We are going to try to buy £5 of this asset which is 0.000025 in quantity
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });
        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 1,
            side: "Sell",
            consideration: {
              currency: "GBP",
              amount: 5000
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);
        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should be no remaining amount left", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        let amountOrderWasSold = 0;
        let amountOrderWasBought = 0;

        expect(buyOrdersBefore).toHaveLength(0);

        await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        const sellOrderAfterProcessing = await Order.find({ side: "Sell" });
        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });

        expect(buyOrdersAfterProcessing).toHaveLength(3);
        sellOrderAfterProcessing.forEach((sellOrder) => {
          amountOrderWasSold = amountOrderWasSold + (sellOrder.consideration?.amount as number);
        });
        buyOrdersAfterProcessing.forEach((buyOrder) => {
          amountOrderWasBought = amountOrderWasBought + (buyOrder.consideration?.originalAmount as number);
        });
        const remainingAmount = amountOrderWasSold - amountOrderWasBought;
        expect(remainingAmount).toEqual(0);
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and the endpoint is hit twice", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            consideration: {
              amount: 600,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            consideration: {
              amount: 600,
              currency: "GBP"
            },
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 both times and create buy orders once", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        const secondResponse = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);
        expect(secondResponse.status).toEqual(204);

        const buyOrdersAfterProcessing = await Order.find({ side: "Buy" });

        expect(buyOrdersAfterProcessing).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_uk"].isin,
              consideration: expect.objectContaining({
                amount: expect.anything(),
                currency: "GBP"
              }),
              side: "Buy"
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: expect.anything(),
                currency: "GBP"
              }),
              side: "Buy"
            })
          ])
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingSell status and creating buy orders fails the first time", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        // We mock order creation to fail the first time
        const originalFunction = OrderService.createDbOrder;
        let firstTimeCreatingOrderToReject = true;
        jest.spyOn(OrderService, "createDbOrder").mockImplementation((order: OrderDTOInterface) => {
          if (order.isin === ASSET_CONFIG["corporate_bonds_uk"].isin && firstTimeCreatingOrderToReject) {
            firstTimeCreatingOrderToReject = false;
            return Promise.reject("error when creating order for corporate_bonds_uk");
          } else return originalFunction(order);
        });

        const user = await buildUser({ hasConvertedPortfolio: true });
        await buildSubscription({ owner: user.id });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 },
          { assetId: "corporate_bonds_uk", quantity: 1, price: 26, percentage: 30 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingSell",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            consideration: {
              currency: "GBP",
              amount: 600
            },
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            consideration: {
              currency: "GBP",
              amount: 600
            },
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 both times and create buy orders once", async () => {
        const buyOrdersBefore = await Order.find({ side: "Buy" });
        expect(buyOrdersBefore).toHaveLength(0);

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        expect(response.status).toEqual(204);

        const buyOrdersAfterFirstRun = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterFirstRun).toEqual([
          expect.objectContaining({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: expect.objectContaining({
              currency: "GBP",
              amount: 680,
              originalAmount: 680
            }),
            side: "Buy"
          })
        ]);

        const rebalanceTransactionAfterFirstRun = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterFirstRun.rebalanceStatus).toEqual("PendingSell");

        const secondResponse = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        expect(secondResponse.status).toEqual(204);

        const buyOrdersAfterSecondRun = await Order.find({ side: "Buy" });
        expect(buyOrdersAfterSecondRun).toEqual(
          expect.arrayContaining([
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["corporate_bonds_uk"].isin,
              consideration: expect.objectContaining({
                amount: 520,
                originalAmount: 520,
                currency: "GBP"
              }),
              side: "Buy"
            }),
            expect.objectContaining({
              transaction: rebalanceTransaction._id,
              isin: ASSET_CONFIG["equities_eu"].isin,
              consideration: expect.objectContaining({
                amount: 680,
                currency: "GBP",
                originalAmount: 680
              }),
              side: "Buy"
            })
          ])
        );

        const rebalanceTransactionAfterSecondRun = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterSecondRun.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status and there are buy orders without a WK ID", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              currency: "GBP",
              amount: 520
            },
            side: "Buy"
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: {
              currency: "GBP",
              amount: 680
            },
            side: "Buy"
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not move status to Settled", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status and there are pending buy orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveOrder")
          .mockResolvedValue(buildWealthkernelOrderResponse({ status: "Pending", side: "Buy" }));

        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              currency: "GBP",
              amount: 520
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: {
              currency: "GBP",
              amount: 680
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Pending", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and not move status to Settled", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status and there are no buy orders", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        jest
          .spyOn(WealthkernelService.UKInstance, "retrieveOrder")
          .mockResolvedValue(buildWealthkernelOrderResponse({ status: "Pending", side: "Buy" }));

        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and move status to Settled", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Settled");
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status and all buy orders are rejected", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();

      beforeEach(async () => {
        const user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 10, reserved: 10, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              currency: "GBP",
              amount: 520
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: {
              currency: "GBP",
              amount: 680
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Rejected", id: "wealthkernelId", submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204 and update status to rejected", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Rejected");
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status and all buy orders are matched", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;
      let user: UserDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();
      const EQUITIES_EU_ORDER_ID = "equities_eu";
      const EQUITIES_UK_ORDER_ID = "equities_uk";

      beforeEach(async () => {
        jest.spyOn(eventEmitter, "emit");

        user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          })),
          fees: {
            fx: {
              currency: "GBP",
              amount: MINIMUM_FX_FEE
            },
            commission: {
              currency: "GBP",
              amount: MINIMUM_COMMISSION_FEE
            }
          }
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              currency: "GBP",
              amount: 520
            },
            side: "Buy",
            quantity: 0.2,
            providers: { wealthkernel: { status: "Matched", id: EQUITIES_UK_ORDER_ID, submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: {
              currency: "GBP",
              amount: 680
            },
            quantity: 0.48,
            side: "Buy",
            providers: { wealthkernel: { status: "Matched", id: EQUITIES_EU_ORDER_ID, submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, update portfolio holdings & cash, emit event and update status to settled", async () => {
        const settledAt = new Date(Date.UTC(2022, 1, 14));
        Date.now = jest.fn(() => settledAt.valueOf());

        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        expect(response.status).toEqual(204);

        expect(eventEmitter.emit).toHaveBeenCalledWith(
          events.transaction.rebalanceTransactionSuccess.eventId,
          expect.objectContaining({ id: user.id }),
          expect.objectContaining({ fxFees: MINIMUM_FX_FEE, commissionFees: MINIMUM_COMMISSION_FEE }),
          { triggeredByAutomation: false }
        );

        const updatedPortfolio = await Portfolio.findById(rebalanceTransaction.portfolio);
        expect(updatedPortfolio).toEqual(
          expect.objectContaining({
            cash: expect.objectContaining({
              GBP: expect.objectContaining({
                available: 0,
                reserved: 0,
                settled: 0
              })
            }),
            holdings: expect.arrayContaining([
              expect.objectContaining({
                assetCommonId: "commodities_gold",
                quantity: 2.5
              }),
              expect.objectContaining({
                assetCommonId: "equities_global",
                quantity: 0.8333
              }),
              expect.objectContaining({
                assetCommonId: "equities_uk",
                quantity: 1.2
              }),
              expect.objectContaining({
                assetCommonId: "equities_eu",
                quantity: 1.48
              })
            ])
          })
        );

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("Settled");
        expect(rebalanceTransactionAfterProcess.settledAt).toEqual(settledAt);
      });
    });

    describe("when there is a rebalance transaction with a PendingBuy status, WK fails, and the endpoint is hit twice", () => {
      let rebalanceTransaction: RebalanceTransactionDocument;
      let user: UserDocument;

      const WEALTHKERNEL_PORTFOLIO_ID = faker.string.uuid();
      const EQUITIES_EU_ORDER_ID = "equities_eu";
      const EQUITIES_UK_ORDER_ID = "equities_uk";

      beforeEach(async () => {
        jest.spyOn(eventEmitter, "emit");
        jest.spyOn(WealthkernelService.UKInstance, "retrieveOrder").mockImplementation(() => {
          throw new Error();
        });

        user = await buildUser({ hasConvertedPortfolio: true });

        const ASSET_COMMON_IDS_CONFIG: {
          assetId: investmentUniverseConfig.AssetType;
          quantity: number;
          price: number;
          percentage: number;
        }[] = [
          { assetId: "commodities_gold", quantity: 3, price: 10, percentage: 25 },
          { assetId: "equities_global", quantity: 1, price: 30, percentage: 25 },
          { assetId: "equities_uk", quantity: 1, price: 26, percentage: 30 },
          { assetId: "equities_eu", quantity: 1, price: 14, percentage: 20 }
        ];

        const portfolio = await buildPortfolio({
          owner: user.id,
          cash: { GBP: { available: 0, reserved: 0, settled: 0 } },
          providers: { wealthkernel: { id: WEALTHKERNEL_PORTFOLIO_ID, status: "Active" } },
          mode: PortfolioModeEnum.REAL,
          holdings: await Promise.all(
            ASSET_COMMON_IDS_CONFIG.map(({ assetId, quantity, price }) =>
              buildHoldingDTO(true, assetId, quantity, { price })
            )
          )
        });

        rebalanceTransaction = await buildRebalanceTransaction({
          portfolio: portfolio,
          owner: user,
          rebalanceStatus: "PendingBuy",
          targetAllocation: ASSET_COMMON_IDS_CONFIG.map((config) => ({
            assetCommonId: config.assetId,
            percentage: config.percentage
          }))
        });

        await Promise.all([
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["commodities_gold"].isin,
            quantity: 0.5,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_global"].isin,
            quantity: 0.1667,
            side: "Sell",
            providers: { wealthkernel: { status: "Matched", id: "wealthkernelId", submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_uk"].isin,
            consideration: {
              currency: "GBP",
              amount: 520
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Matched", id: EQUITIES_UK_ORDER_ID, submittedAt: new Date() } }
          }),
          buildOrder({
            transaction: rebalanceTransaction._id,
            isin: ASSET_CONFIG["equities_eu"].isin,
            consideration: {
              currency: "GBP",
              amount: 680
            },
            side: "Buy",
            providers: { wealthkernel: { status: "Matched", id: EQUITIES_EU_ORDER_ID, submittedAt: new Date() } }
          })
        ]);

        await buildIntraDayPortfolioTicker({
          portfolio: portfolio._id,
          timestamp: new Date(),
          pricePerCurrency: { GBP: 100 }
        });
      });

      it("should return a 204, not update portfolio holdings & cash, not emit event and update status to settled", async () => {
        const response = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");
        const secondResponse = await request(app)
          .post("/api/admin/m2m/transactions/rebalances/process")
          .set("Accept", "application/json");

        expect(response.status).toEqual(204);
        expect(secondResponse.status).toEqual(204);

        expect(eventEmitter.emit).not.toHaveBeenCalled();

        const rebalanceTransactionAfterProcess = (await RebalanceTransaction.findById(
          rebalanceTransaction.id
        )) as RebalanceTransactionDocument;
        expect(rebalanceTransactionAfterProcess.rebalanceStatus).toEqual("PendingBuy");
      });
    });
  });

  describe("POST /transactions/deposits/sync-truelayer", () => {
    let deposits: DepositCashTransactionDocument[];
    let portfolio: PortfolioDocument;
    let user: UserDocument;
    let response: request.Response;

    beforeEach(async () => {
      const DATE = new Date("2022-08-31T11:00:00Z");

      jest.clearAllMocks();
      Date.now = jest.fn(() => DATE.valueOf());

      const wealthKernelProtfolioId = faker.string.uuid();
      portfolio = await buildPortfolio({
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: wealthKernelProtfolioId, status: "Active" } }
      });
      user = await buildUser();

      deposits = await Promise.all(
        ["authorization_required", "authorizing", "authorized"].map((status) =>
          buildDepositCashTransaction({
            portfolio,
            owner: user,
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: status as PaymentStatusTypeV3,
                version: "v3"
              }
            },
            consideration: {
              currency: "GBP",
              amount: faker.number.int({ min: 1, max: 20 })
            },
            createdAt: new Date(DateUtil.getDateOfMinutesAgo(15))
          })
        )
      );

      const trueLayerResponse = buildPaymentTypeResults(buildPaymentType({ status: "executed" }));
      TruelayerPaymentsClient.prototype.getPayment = jest.fn((): any => {
        return trueLayerResponse;
      });

      response = await request(app)
        .post("/api/admin/m2m/transactions/deposits/sync-truelayer")
        .set("Accept", "application/json");
    });
    afterEach(async () => await clearDb());

    it("should have status 204", async () => {
      expect(response.status).toEqual(204);
    });

    it("should update the deposit document with latest truelayer status of any deposits with truelayer status 'authorization_required', 'authorizing' or 'authorized'", async () => {
      const updatedTransactions = await DepositCashTransaction.find({
        "providers.truelayer.status": "executed"
      });

      expect(updatedTransactions).toHaveLength(deposits.length);

      const updatedTransactionsIds = updatedTransactions.map((transaction) => transaction.id);
      deposits.forEach((transaction) => {
        expect(updatedTransactionsIds).toContain(transaction.id);
      });
    });
  });

  describe("/transactions/deposits/sync-wealthkernel", () => {
    const DATE = new Date("2022-08-31T11:00:00Z");
    const CREATED_WK_DEPOSIT_IDs = ["deposit-1-created", "deposit-2-created"];
    let minimumCreationTime: Date;
    let user: UserDocument;
    let bankAccount: BankAccountDocument;
    let portfolio: PortfolioDocument;
    let response: request.Response;

    beforeEach(async () => {
      jest.clearAllMocks();

      Date.now = jest.fn(() => DATE.valueOf());
      minimumCreationTime = new Date(Date.now() - 16 * 60 * 1000);

      user = await buildUser({}, false);
      bankAccount = await buildBankAccount({
        owner: user.id,
        providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } }
      });
      const wealthkernelPortfolioId = faker.string.uuid();
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: wealthkernelPortfolioId, status: "Active" } },
        cash: {
          GBP: {
            available: 10,
            reserved: 0,
            settled: 0
          }
        }
      });

      // not created deposit
      await buildDepositCashTransaction({
        portfolio,
        bankAccount,
        owner: user,
        providers: {
          truelayer: {
            id: faker.string.uuid(),
            status: "executed"
          }
        },
        consideration: {
          currency: "GBP",
          amount: 5000
        },
        createdAt: minimumCreationTime
      });

      jest.spyOn(eventEmitter, "emit");
      jest.spyOn(logger, "error");
    });
    afterEach(async () => await clearDb());

    describe("when deposits are retrieved from wealthkernel", () => {
      const WK_CONSIDERATION_AMOUNT = 10; // amount of WK should be in pounds

      describe("and there is no transaction that is pending our deposits", () => {
        let createdDeposits: DepositCashTransactionDocument[];

        beforeEach(async () => {
          createdDeposits = await Promise.all([
            ...CREATED_WK_DEPOSIT_IDs.map((wealthkernelDepositId) =>
              buildDepositCashTransaction({
                portfolio,
                bankAccount,
                owner: user,
                providers: {
                  truelayer: {
                    id: faker.string.uuid(),
                    status: "executed"
                  },
                  wealthkernel: {
                    id: wealthkernelDepositId,
                    status: "Created"
                  }
                },
                consideration: {
                  currency: "GBP",
                  amount: 1000
                },
                depositAction: DepositActionEnum.JUST_PAY,
                createdAt: minimumCreationTime
              })
            )
          ]);

          WealthkernelService.UKInstance.retrieveDeposit = jest.fn((): any => {
            return { status: "Settled", consideration: { amount: WK_CONSIDERATION_AMOUNT } };
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/deposits/sync-wealthkernel")
            .set("Accept", "application/json");
        });

        it("should have status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should emit a depositSuccess transaction event for each settled deposit", async () => {
          const updatedDeposits = await DepositCashTransaction.find({
            _id: { $in: createdDeposits.map((transaction) => transaction._id) }
          });
          updatedDeposits.forEach((deposit: DepositCashTransactionDocument) => {
            expect(eventEmitter.emit).toBeCalledWith(
              events.transaction.depositSuccess.eventId,
              expect.objectContaining({
                email: user.email
              }),
              expect.objectContaining({
                amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
                noAssetTransactionPending: true
              })
            );
          });
        });

        it("should emit a depositAvailable transaction event for each settled deposit", async () => {
          const updatedDeposits = await DepositCashTransaction.find({
            _id: { $in: createdDeposits.map((transaction) => transaction._id) }
          });
          updatedDeposits.forEach((deposit: DepositCashTransactionDocument) => {
            expect(eventEmitter.emit).toBeCalledWith(
              events.transaction.depositAvailable.eventId,
              expect.objectContaining({
                email: user.email
              }),
              expect.objectContaining({
                amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
                noAssetTransactionPending: true
              })
            );
          });
        });

        it("should update the deposit document with the settled status for each settled deposit", async () => {
          const updatedDeposits = await DepositCashTransaction.find({
            _id: { $in: createdDeposits.map((transaction) => transaction._id) }
          });
          updatedDeposits.forEach((deposit) => {
            expect(deposit.status).toEqual("Settled");
          });
        });

        it("should increase the available cash by the deposit amount for each settled deposit", async () => {
          const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
          const depositSum = createdDeposits
            .map((deposit) => Decimal.div(deposit.consideration.amount as number, 100))
            .reduce((sum, amount) => sum.plus(amount), new Decimal(0));
          expect(updatedPortfolio.cash.GBP.available).toEqual(
            Decimal.add(portfolio.cash.GBP.available, depositSum).toNumber()
          );
        });
      });

      describe("and deposit is linked to a cancelled asset transaction", () => {
        let deposit: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;

        beforeEach(async () => {
          deposit = await buildDepositCashTransaction(
            {
              portfolio: portfolio.id,
              owner: user.id,
              bankAccount,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                },
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Created"
                }
              },
              consideration: {
                currency: "GBP",
                amount: 1000
              },
              createdAt: minimumCreationTime,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST
            },
            user,
            portfolio
          );
          assetTransaction = await buildAssetTransaction({
            pendingDeposit: deposit.id,
            portfolio: portfolio.id,
            owner: user.id,
            status: "Cancelled"
          });
          assetTransaction.orders = [
            await buildOrder({
              side: "Buy",
              status: "Cancelled",
              transaction: assetTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_uk"].isin,
              consideration: {
                originalAmount: 500,
                amount: 489,
                currency: "GBP"
              }
            }),
            await buildOrder({
              side: "Buy",
              status: "Cancelled",
              transaction: assetTransaction.id,
              isin: investmentUniverseConfig.ASSET_CONFIG["equities_us"].isin,
              consideration: {
                originalAmount: 500,
                amount: 492,
                currency: "GBP"
              }
            })
          ];
          await assetTransaction.save();

          WealthkernelService.UKInstance.retrieveDeposit = jest.fn((): any => {
            return { status: "Settled", consideration: { amount: WK_CONSIDERATION_AMOUNT } };
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/deposits/sync-wealthkernel")
            .set("Accept", "application/json");
        });

        it("should have status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should emit a depositSuccess transaction event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositSuccess.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: true
            })
          );
        });

        it("should emit a depositAvailable transaction event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositAvailable.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: true
            })
          );
        });

        it("should update the deposit document with the settled status", async () => {
          const updatedDeposit = await DepositCashTransaction.findById(deposit.id);

          expect(updatedDeposit.status).toEqual("Settled");
          expect(updatedDeposit.settledAt).toEqual(DATE);
        });

        it("should NOT update the asset transaction", async () => {
          const updatedAssetTransaction = await AssetTransaction.find({
            _id: assetTransaction.id
          });
          expect(updatedAssetTransaction[0].status).toEqual("Cancelled");
        });

        it("should increase the available cash of the user", async () => {
          const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
          expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available + 10);
        });
      });

      describe("and deposit is linked to a pending deposit asset transaction", () => {
        let deposit: DepositCashTransactionDocument;
        let assetTransaction: AssetTransactionDocument;

        beforeEach(async () => {
          deposit = await buildDepositCashTransaction(
            {
              portfolio: portfolio.id,
              owner: user.id,
              bankAccount,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                },
                wealthkernel: {
                  id: faker.string.uuid(),
                  status: "Created"
                }
              },
              consideration: {
                currency: "GBP",
                amount: 1000
              },
              createdAt: minimumCreationTime,
              depositAction: DepositActionEnum.DEPOSIT_AND_INVEST
            },
            user,
            portfolio
          );
          assetTransaction = await buildAssetTransaction({
            pendingDeposit: deposit.id,
            portfolio: portfolio.id,
            owner: user.id,
            status: "PendingDeposit"
          });

          WealthkernelService.UKInstance.retrieveDeposit = jest.fn((): any => {
            return { status: "Settled", consideration: { amount: WK_CONSIDERATION_AMOUNT } };
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/deposits/sync-wealthkernel")
            .set("Accept", "application/json");
        });

        it("should have status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should emit a depositSuccess transaction event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledTimes(4);
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositAvailable.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: false
            })
          );
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositSuccess.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(deposit.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: false
            })
          );
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.firstInvestmentCreation.eventId,
            expect.objectContaining({ _id: portfolio.owner._id })
          );
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({ _id: portfolio.owner._id }),
            expect.objectContaining({})
          );
        });

        it("should update the deposit document with the settled status", async () => {
          const updatedDeposit = await DepositCashTransaction.findById(deposit.id);

          expect(updatedDeposit.status).toEqual("Settled");
          expect(updatedDeposit.settledAt).toEqual(DATE);
        });

        it("should update the asset transaction", async () => {
          const updatedAssetTransaction = await AssetTransaction.find({
            _id: assetTransaction.id
          });
          expect(updatedAssetTransaction[0].status).toEqual("Pending");
        });

        it("should NOT increase the available cash of the user", async () => {
          const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
          expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
        });
      });

      describe("and deposit is linked to a savings top-up transaction", () => {
        let savingsTopupTransaction: SavingsTopupTransactionDocument;
        let pendingDeposit: DepositCashTransactionDocument;

        beforeEach(async () => {
          pendingDeposit = await buildDepositCashTransaction({
            portfolio,
            owner: user,
            bankAccount,
            providers: {
              truelayer: {
                id: faker.string.uuid(),
                status: "executed"
              },
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Created"
              }
            },
            consideration: {
              currency: "GBP",
              amount: 1000
            },
            createdAt: new Date(Date.now() - 16 * 60 * 1000),
            depositAction: DepositActionEnum.DEPOSIT_AND_SAVE
          });
          savingsTopupTransaction = await buildSavingsTopup({
            pendingDeposit: pendingDeposit._id,
            owner: pendingDeposit.owner.id,
            status: "PendingDeposit"
          });
          WealthkernelService.UKInstance.retrieveDeposit = jest.fn((): any => {
            return { status: "Settled", consideration: { amount: WK_CONSIDERATION_AMOUNT } };
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/deposits/sync-wealthkernel")
            .set("Accept", "application/json");
        });

        it("should have status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should emit a depositSuccess event", async () => {
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositAvailable.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(pendingDeposit?.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: false
            })
          );
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.depositSuccess.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              amount: Decimal.div(pendingDeposit?.consideration.amount as number, 100).toNumber(),
              noAssetTransactionPending: false
            })
          );
          expect(eventEmitter.emit).toHaveBeenCalledWith(
            events.transaction.investmentCreation.eventId,
            expect.objectContaining({
              email: user.email
            }),
            expect.objectContaining({
              side: "buy",
              category: "savings",
              assetName: "mmf_dist_gbp",
              amount: Decimal.div(savingsTopupTransaction.consideration.amount, 100).toNumber(),
              cashbackAmount: 0,
              fxFees: 0,
              commissionFees: 0,
              executionSpreadFees: 0,
              frequency: "one-off"
            })
          );
        });

        it("should settle the deposit", async () => {
          const updatedDeposit = await DepositCashTransaction.findById(pendingDeposit._id);

          expect(updatedDeposit?.status).toEqual("Settled");
          expect(updatedDeposit?.settledAt).toEqual(DATE);
        });

        it("should update the AssetTransaction document with the pending status instead of pendingDeposit", async () => {
          const updatedSavingsTopupTransaction = await SavingsTopupTransaction.findById(
            savingsTopupTransaction._id
          );
          expect(updatedSavingsTopupTransaction?.status).toEqual("Pending");
        });

        it("should not increase the available cash", async () => {
          const updatedPortfolio = (await Portfolio.findOne({ _id: portfolio._id })) as PortfolioDocument;
          expect(updatedPortfolio.cash.GBP.available).toEqual(portfolio.cash.GBP.available);
        });
      });
    });

    describe("when deposit are not retrieved from wealthkernel", () => {
      let createdDeposits: DepositCashTransactionDocument[];

      beforeEach(async () => {
        createdDeposits = await Promise.all([
          ...CREATED_WK_DEPOSIT_IDs.map((wealthkernelDepositId) =>
            buildDepositCashTransaction({
              portfolio,
              owner: user,
              providers: {
                truelayer: {
                  id: faker.string.uuid(),
                  status: "executed"
                },
                wealthkernel: {
                  id: wealthkernelDepositId,
                  status: "Created"
                }
              },
              consideration: {
                currency: "GBP",
                amount: 1000
              },
              createdAt: minimumCreationTime
            })
          )
        ]);
        WealthkernelService.UKInstance.retrieveDeposit = jest.fn((): any => {
          throw new Error("WK error");
        });
        WealthkernelService.UKInstance.retrieveTransaction = jest.fn((): any => {
          return;
        });

        response = await request(app)
          .post("/api/admin/m2m/transactions/deposits/sync-wealthkernel")
          .set("Accept", "application/json");
      });

      it("should have status 204", async () => {
        expect(response.status).toEqual(204);
      });

      it("should skip any deposit syncing and log an error message", () => {
        createdDeposits.forEach((deposit) =>
          expect(logger.error).toHaveBeenCalledWith(
            `Syncing with wealthkernel failed for deposit ${deposit._id}`,
            expect.anything()
          )
        );
      });
    });
  });

  describe("/transactions/assets/convert-users-with-pending-deposits", () => {
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let response: supertest.Response;

    describe("when there are no transactions that were pending deposits", () => {
      beforeAll(async () => {
        portfolio = await buildPortfolio({
          providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
          cash: { GBP: { available: 100, reserved: 100, settled: 0 } }
        });
        await buildAssetTransaction({ owner: portfolio.owner, portfolio: portfolio._id });

        response = await request(app)
          .post("/api/admin/m2m/transactions/assets/convert-users-with-pending-deposits")
          .set("Accept", "application/json");
      });

      it("should return status 204", async () => {
        expect(response.status).toEqual(204);
      });

      it("should not update the status of any user", async () => {
        const usersInProgress = await User.find({ portfolioConversionStatus: "inProgress" });

        expect(usersInProgress.length).toEqual(0);
      });
    });

    describe("when there are transactions that were pending deposits", () => {
      describe("where the users of those transactions are already converted", () => {
        beforeAll(async () => {
          user = await buildUser({ portfolioConversionStatus: "completed" });
          portfolio = await buildPortfolio({
            owner: user._id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
            cash: { GBP: { available: 100, reserved: 100, settled: 0 } }
          });
          const deposit = await buildDepositCashTransaction({
            owner: user._id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            consideration: {
              currency: "GBP",
              amount: 1000
            }
          });
          await buildAssetTransaction({
            owner: user._id,
            status: "Pending",
            portfolio: portfolio._id,
            pendingDeposit: deposit._id
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/assets/convert-users-with-pending-deposits")
            .set("Accept", "application/json");
        });

        it("should return status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should not update the status of any user", async () => {
          const usersInProgress = await User.find({ portfolioConversionStatus: "inProgress" });

          expect(usersInProgress.length).toEqual(0);
        });
      });

      describe("where there is a non-converted user that had a transaction pending deposit which is now pending", () => {
        beforeEach(async () => {
          user = await buildUser({ portfolioConversionStatus: "notStarted" });
          portfolio = await buildPortfolio({
            owner: user._id,
            providers: { wealthkernel: { id: faker.string.uuid(), status: "Active" } },
            cash: { GBP: { available: 100, reserved: 100, settled: 0 } }
          });
          const deposit = await buildDepositCashTransaction({
            owner: user._id,
            providers: {
              wealthkernel: {
                id: faker.string.uuid(),
                status: "Settled"
              }
            },
            consideration: {
              currency: "GBP",
              amount: 1000
            }
          });
          await buildAssetTransaction({
            owner: user._id,
            status: "Pending",
            portfolio: portfolio._id,
            pendingDeposit: deposit._id
          });

          response = await request(app)
            .post("/api/admin/m2m/transactions/assets/convert-users-with-pending-deposits")
            .set("Accept", "application/json");
        });

        it("should return status 204", async () => {
          expect(response.status).toEqual(204);
        });

        it("should update portfolio conversion status of the user", async () => {
          const updatedUser = (await User.findById(user._id)) as UserDocument;

          expect(updatedUser.portfolioConversionStatus).toEqual("inProgress");
        });
      });
    });
  });

  describe("POST /transactions/dividends/fetch-wealthkernel", () => {
    const WK_EXISTING_ID = "existing-id";
    const AVAILABLE_CASH = 100;
    const TODAY = new Date("2022-08-31T11:00:00Z");

    let response: supertest.Response;
    let user: UserDocument;
    let portfolio: PortfolioDocument;
    let dividendStoredInDb: TransactionType;
    let dividendSettled: TransactionType;
    let dividendCancelled: TransactionType;
    let dividendWithSavingsDividend: TransactionType;

    beforeAll(async () => {
      jest.clearAllMocks();

      Date.now = jest.fn(() => TODAY.valueOf());

      jest.spyOn(RedisClientService.Instance, "del");
      jest.spyOn(eventEmitter, "emit");

      const WK_PORTFOLIO_ID = faker.string.uuid();
      user = await buildUser({ kycStatus: "passed" });
      portfolio = await buildPortfolio({
        owner: user.id,
        mode: PortfolioModeEnum.REAL,
        providers: { wealthkernel: { id: WK_PORTFOLIO_ID, status: "Active" } },
        cash: { GBP: { available: AVAILABLE_CASH, reserved: 0, settled: 0 } }
      });
      await buildDividendTransaction({ providers: { wealthkernel: { id: WK_EXISTING_ID, status: "Settled" } } });

      // 1. wk - create dividend response with transaction id that already exists in db
      dividendStoredInDb = buildWealthkernelTransactionResponse({
        type: "Dividend",
        id: WK_EXISTING_ID,
        status: "Settled",
        portfolioId: WK_PORTFOLIO_ID
      });
      // 2. wk - create settled dividend response
      dividendSettled = buildWealthkernelTransactionResponse({
        type: "Dividend",
        status: "Settled",
        portfolioId: WK_PORTFOLIO_ID,
        consideration: {
          currency: CurrencyEnum.GBP,
          amount: 1
        }
      });
      // 3. wk - create cancelled dividend response
      dividendCancelled = buildWealthkernelTransactionResponse({
        type: "Dividend",
        status: "Cancelled",
        portfolioId: WK_PORTFOLIO_ID
      });
      // 4. wk - create dividend with savings isin response
      dividendWithSavingsDividend = buildWealthkernelTransactionResponse({
        type: "Dividend",
        status: "Cancelled",
        portfolioId: WK_PORTFOLIO_ID,
        isin: savingsUniverseConfig.SAVINGS_PRODUCT_CONFIG_GLOBAL["mmf_dist_gbp"].isin
      });
      const transactionResponses = [
        dividendStoredInDb,
        dividendSettled,
        dividendCancelled,
        dividendWithSavingsDividend
      ];

      jest
        .spyOn(WealthkernelService.UKInstance, "listTransactions")
        .mockImplementation(async (options, processor): Promise<void> => {
          if (processor) {
            for (let i = 0; i < transactionResponses.length; i++) {
              const transaction = transactionResponses[i];
              await processor(transaction);
            }
          }
        });
      jest.spyOn(WealthkernelService.EUInstance, "listTransactions").mockResolvedValue([]);

      const existingDbDividends = await DividendTransaction.find({});
      expect(existingDbDividends.length).toBe(1);

      response = await request(app)
        .post("/api/admin/m2m/transactions/dividends/fetch-wealthkernel")
        .set("Accept", "application/json");
    });
    afterAll(async () => await clearDb());

    it("should return status 204", () => {
      expect(response.status).toEqual(204);
    });

    it("should create dividend documents for all dividends returned from wealthkernel, skipping the existing created ones and the ones with savings isin", async () => {
      const dividends = (await DividendTransaction.find({})) as DividendTransactionDocument[];

      expect(
        dividends.find((dividend) => dividend.providers.wealthkernel.id === dividendSettled.id).settledAt
      ).toEqual(TODAY);
      expect(
        dividends.find((dividend) => dividend.providers.wealthkernel.id === dividendCancelled.id).settledAt
      ).toBeUndefined();

      const wkDividendIDs = new Set(dividends.map((dividend) => dividend.providers?.wealthkernel?.id));
      expect(wkDividendIDs).toEqual(new Set([dividendStoredInDb.id, dividendSettled.id, dividendCancelled.id]));
      expect(wkDividendIDs).not.toContain(new Set([dividendWithSavingsDividend.id]));
    });

    it("should skip dividend creation with warn log for transaction id that already exists in the database", () => {
      expect(logger.warn).toHaveBeenCalledWith(
        `Dividend ${dividendStoredInDb.id} already has a db document - aborting`,
        expect.objectContaining({
          module: "TransactionService",
          method: "_createInvestmentDividend"
        })
      );
    });

    it("should increase the portfolio cash by the dividend amount for Settled dividends", async () => {
      const updatedPortfolio = await Portfolio.findOne({ _id: portfolio.id });
      expect(updatedPortfolio?.cash.GBP.available).toEqual(
        Decimal.sum(AVAILABLE_CASH, dividendSettled.consideration.amount).toNumber()
      );
    });

    it("should emit dividend 'onDividendSuccess' events for Settled dividends", async () => {
      expect(eventEmitter.emit).toHaveBeenNthCalledWith(
        1,
        events.transaction.dividendSuccess.eventId,
        expect.objectContaining({ email: user.email }),
        expect.objectContaining({
          amount: "£1.00"
        })
      );
    });

    it("should clear the redis cache for the portfolio value & mwrr & up by", () => {
      expect(RedisClientService.Instance.del).toHaveBeenCalledWith(`portfolios:mwrr:${portfolio.id}`);
      expect(RedisClientService.Instance.del).toHaveBeenCalledWith(`portfolios:value_at_mwrr:${portfolio.id}`);
      expect(RedisClientService.Instance.del).toHaveBeenCalledWith(`portfolios:up_by:${portfolio.id}`);
      expect(RedisClientService.Instance.del).toHaveBeenCalledWith(`portfolios:value_at_up_by:${portfolio.id}`);
    });
  });
});
