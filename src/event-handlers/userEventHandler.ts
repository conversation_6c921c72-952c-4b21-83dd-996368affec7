import eventEmitter from "../loaders/eventEmitter";
import { ParticipantDocument } from "../models/Participant";
import { KycStatusEnum, UserDocument } from "../models/User";
import FinanceAdsService from "../external-services/financeAdsService";
import FacebookAppEventService from "../external-services/facebookAppEventService";
import GoogleAnalyticsService, { EventActionEnum } from "../external-services/googleAnalyticsService";
import logger from "../external-services/loggerService";
import analytics, {
  MixpanelAccountStatusEnum,
  TrackBankAccountPropertiesType,
  TrackRiskAssessmentType,
  UserTraitType
} from "../external-services/segmentAnalyticsService";
import { ReferredStatus } from "referral";
import { hashSHA256 } from "../utils/cryptoUtil";
import events from "./events";
import { TransactionalNotificationEventEnum } from "./notificationEvents";
import { GiftDocument } from "../models/Gift";
import UserService from "../services/userService";
import { KycOperationDocument } from "../models/KycOperation";
import { RiskAssessmentDocument } from "../models/RiskAssessment";
import { UserDataRequestReasonEnum } from "../models/UserDataRequest";
import SubmissionTechEventService, {
  SubmissionTechEventEnum
} from "../external-services/submissionTechEventService";
import NotificationService from "../services/notificationService";

class UserEventHandler {
  constructor() {
    eventEmitter.on(events.user.bankAccountLinking.eventId, this._handleBankAccountLinking.bind(this));
    eventEmitter.on(events.user.bankAccountRemoval.eventId, this._handleBankAccountRemoval.bind(this));
    eventEmitter.on(
      events.user.deletionFeedbackSubmission.eventId,
      this._handleDeletionFeedbackSubmission.bind(this)
    );
    eventEmitter.on(
      events.user.personalDetailsSubmission.eventId,
      this._handlePersonalDetailsSubmission.bind(this)
    );
    eventEmitter.on(
      events.user.passportDetailsSubmission.eventId,
      this._handlePassportDetailsSubmission.bind(this)
    );
    eventEmitter.on(events.user.addressSubmission.eventId, this._handleAddressSubmission.bind(this));
    eventEmitter.on(events.user.taxDetailsSubmission.eventId, this._handleTaxDetailsSubmission.bind(this));
    eventEmitter.on(events.user.logIn.eventId, this._handleUserLogin.bind(this));
    eventEmitter.on(events.user.signUp.eventId, this._handleUserSignUp.bind(this));
    eventEmitter.on(events.user.verification.eventId, this._handleUserVerification.bind(this));
    eventEmitter.on(events.user.verificationFailure.eventId, this._handleUserVerificationFailure.bind(this));
    eventEmitter.on(events.user.welcome.eventId, this._handleUserWelcome.bind(this));
    eventEmitter.on(events.user.accountSuspended.eventId, this._handleAccountSuspended.bind(this));
    eventEmitter.on(events.user.disassociation.eventId, this._handleUserDisassociation.bind(this));
    eventEmitter.on(events.user.residencyCountryChange.eventId, this._handleResidencyCountryChanged.bind(this));
    eventEmitter.on(events.user.referralCodeSubmission.eventId, this._handleReferralCodeSubmission.bind(this));
    eventEmitter.on(events.user.friendInvitation.eventId, this._handleFriendInvitation.bind(this));
    eventEmitter.on(events.user.employmentInfoSubmission.eventId, this._handleEmploymentInfoSubmission.bind(this));
    eventEmitter.on(events.user.termsAccepted.eventId, this._handleAcceptedTerms.bind(this));
    eventEmitter.on(events.user.sumsubKycStarted.eventId, this._handleSumsubKycStart.bind(this));
    eventEmitter.on(events.user.sumsubKycFinished.eventId, this._handleSumsubKycFinish.bind(this));
    eventEmitter.on(events.user.riskAssessmentCreated.eventId, this._handleRiskAssessmentCreation.bind(this));
    eventEmitter.on(
      events.user.highRiskAssessmentDetected.eventId,
      this._handleHighRiskAssessmentDetection.bind(this)
    );
    eventEmitter.on(events.user.wkAccountClosed.eventId, this._handleWkAccountClosed.bind(this));
    eventEmitter.on(events.user.wealthybitesSubscription.eventId, this._handleWealthybitesSubscription.bind(this));
    eventEmitter.on(
      events.user.promotionalEmailSubscription.eventId,
      this._handlePromotionalEmailSubscription.bind(this)
    );
    eventEmitter.on(events.user.whAccountStatusUpdate.eventId, this._handleWhAccountStatusUpdate.bind(this));
    eventEmitter.on(events.user.joinedWaitingList.eventId, this._handleJoinedWaitingList.bind(this));
    eventEmitter.on(events.user.usedWhitelistCode.eventId, this._handleUsedWhitelistCode.bind(this));
    eventEmitter.on(events.user.usedWhitelistedEmail.eventId, this._handleUsedWhitelistedEmail.bind(this));
  }

  private async _handleBankAccountLinking(
    user: UserDocument,
    properties: {
      isFirst: boolean;
      bankLinkedFrom: string;
      hasSettledRewards: boolean;
      banks: string[];
      hasUsedGift: boolean;
    } & TrackBankAccountPropertiesType
  ): Promise<void> {
    logger.info(`Bank account linked for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleBankAccountLinking",
      data: {
        ...properties
      }
    });
    const { bankLinkedFrom, isFirst, banks, truelayerId, hasUsedGift } = properties;

    // We only update the Mailchimp status of the user if:
    // a) it is the first time the user links a bank account - if it is not, the user is already in Bank Account Linked status or later e.g. Investment Created.
    // b) the user does not have a settled reward - if they do, they're already in Investment Created status or later
    // c) the user is not gifted - because in that case he is already in the Investment Created Status or later
    if (isFirst && !properties.hasSettledRewards && !hasUsedGift) {
      analytics.identify(
        user,
        { status: events.user.bankAccountLinking.name, bankLinked: bankLinkedFrom, banks },
        { All: false, Mixpanel: true, Intercom: true }
      );
      analytics.identify(user, { bankLinked: bankLinkedFrom, banks }, { All: false, MailChimp: true });
    } else {
      analytics.identify(
        user,
        { bankLinked: bankLinkedFrom, banks },
        { All: false, MailChimp: true, Mixpanel: true }
      );
    }
    analytics.track(
      user,
      events.user.bankAccountLinking.name,
      { All: false, Mixpanel: true, Slack: true },
      { truelayerId }
    );
    await Promise.allSettled([
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.BANK
      }),
      FacebookAppEventService.trackEvent(events.user.bankAccountLinking.name, user.lastLoginPlatform, {
        email: user.email
      })
    ]);
  }

  private async _handleBankAccountRemoval(
    user: UserDocument,
    properties: { banks: string[] } & TrackBankAccountPropertiesType
  ): Promise<void> {
    logger.info(`Bank account removed for user ${user.email}`, {
      module: "TransactionEventHandler",
      method: "_handleBankAccountRemoval",
      data: {
        ...properties
      }
    });

    analytics.identify(user, { banks: properties.banks }, { All: false, Mixpanel: true });
    analytics.track(
      user,
      events.user.bankAccountRemoval.name,
      { All: false, Mixpanel: true },
      { truelayerId: properties.truelayerId }
    );
  }

  private async _handleDeletionFeedbackSubmission(user: UserDocument): Promise<void> {
    analytics.identify(user, { deletionFeedback: user.deletionFeedback }, { All: false, Mixpanel: true });
    analytics.track(
      user,
      events.user.deletionFeedbackSubmission.name,
      { All: false, Mixpanel: true, Slack: true },
      { deletionFeedback: user.deletionFeedback }
    );
  }

  private async _handlePersonalDetailsSubmission(user: UserDocument): Promise<void> {
    analytics.track(user, events.user.personalDetailsSubmission.name, { Slack: false });
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.PERSONAL_DETAILS
    });
  }

  private async _handlePassportDetailsSubmission(user: UserDocument): Promise<void> {
    analytics.identify(
      user,
      {
        status: events.user.passportDetailsSubmission.name,
        dateOfBirth: user.dateOfBirth,
        firstName: user.firstName,
        lastName: user.lastName,
        nation: user.nationalities[0]
      },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );
    analytics.track(user, events.user.passportDetailsSubmission.name);
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.PASSPORT_DETAILS
    });

    // This event is sent to Google in order to start optimizing for users
    // that are > 24yo, as a workaround for sending age to Google, because the
    // age filter is not working well for Google Ads.
    if (UserService.isUserOverAgeX(user, 24)) {
      await GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.PASSPORT_DETAILS_OVER_24
      });
    }
  }

  private async _handleAddressSubmission(user: UserDocument): Promise<void> {
    analytics.identify(
      user,
      { status: events.user.addressSubmission.name },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );
    analytics.track(user, events.user.addressSubmission.name);
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.ADDRESS
    });
  }

  private async _handleTaxDetailsSubmission(user: UserDocument): Promise<void> {
    analytics.identify(
      user,
      { status: events.user.taxDetailsSubmission.name, kyc: KycStatusEnum.PENDING },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );
    analytics.track(user, events.user.taxDetailsSubmission.name);
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.TAX_DETAILS
    });
  }

  private async _handleUserLogin(user: UserDocument, properties: { justDownloadedApp: boolean }): Promise<void> {
    const traits: UserTraitType = {};

    // We only want to populate the mobileApp field for Android and iOS platforms
    if (!user.participant) {
      await user.populate("participant");
      if (!user.participant) {
        // If participant is still undefined something is very wrong - log error
        logger.error(`Participant doesn't exist for user ${user.email}`, {
          module: "UserEventHandler",
          method: "_handleUserLogin"
        });
        return;
      }
    }

    const appInstallInfo = user.participant?.appInstallInfo;
    if (appInstallInfo) {
      traits.appDownld = appInstallInfo.createdAt;
      traits.mobileApp = appInstallInfo.platform;
    }

    const { justDownloadedApp } = properties;
    if (justDownloadedApp) {
      // In this scenario we already have an identified user with user id due to the sign up from web.
      // We also have an identified user with anonymous id the appsflyer id during the app install event.
      // This is the point where the user has just downloaded the app, so we merge their anonymous id with
      // their known user id.
      const previousId = user.participant?.appsflyerId;
      const userId = user.id;
      analytics.alias({ previousId, userId });
      await analytics.delay(2000);
    }

    analytics.identify(user, traits, { All: false, MailChimp: true, Mixpanel: true });
    analytics.track(user, events.user.logIn.name, { All: false, Mixpanel: true });
  }

  private async _handleUserSignUp(
    user: UserDocument,
    data: { referredStatus: ReferredStatus; gift: GiftDocument }
  ): Promise<void> {
    // check user participant is populated
    if (!user.participant) {
      logger.warn(`Participant should be populated for ${user.email}`, {
        module: "UserEventHandler",
        method: "_handleUserSignUp"
      });
      await user.populate([
        {
          path: "participant",
          populate: [{ path: "referrer" }]
        }
      ]);

      if (!user.participant) {
        // If participant is still undefined something is very wrong - log error
        logger.error(`Participant doesn't exist for user ${user.email}`, {
          module: "UserEventHandler",
          method: "_handleUserSignUp"
        });
      }
    }

    const influencerId = user.participant?.metadata?.financeAds?.influencerId;
    const pageUserLanded = user.participant?.pageUserLanded;
    const traits: UserTraitType = {
      status: events.user.signUp.name,
      appsflyerId: user?.participant?.appsflyerId,
      email: user.email,
      emailVerif: user.emailVerified ? "True" : "",
      financeAdsId: influencerId,
      referred: data.referredStatus,
      referredBy: (user.participant?.referrer as ParticipantDocument)?.email,
      pageUserLanded,
      gifted: data.gift ? "True" : "False",
      giftedBy: (data.gift?.gifter as UserDocument)?.email,
      signedupAt: user.createdAt,
      source: user?.participant?.trackingSource,
      sourceCampaign: user?.participant?.metadata?.googleAds?.campaign,
      submissionTechClickId: user?.participant?.metadata?.submissionTech?.clickId
    };

    // We only want to populate the mobileApp field for Android and iOS platforms
    const appInstallInfo = user.participant?.appInstallInfo;
    if (appInstallInfo) {
      traits.appDownld = appInstallInfo.createdAt;
      traits.mobileApp = appInstallInfo.platform;
    }

    // associate anonymous id with user document id
    let previousId: string;
    if (user?.participant?.appsflyerId) {
      previousId = user?.participant?.appsflyerId;
    } else {
      previousId = hashSHA256(user.email);
    }

    // We wait before and after aliasing to allow more time for Segment to process the events
    await analytics.delay(2000);
    analytics.alias({ previousId, userId: user.id.toString() });
    await analytics.delay(4000);
    analytics.identify(user, traits, { Slack: false });

    // In addition to the above, we also identify pageLanded to Mailchimp as it is the shortened version of
    // pageUserLanded that is set as the merge field in Mailchimp.
    analytics.identify(
      user,
      {
        pageLanded: pageUserLanded
      },
      { All: false, MailChimp: true }
    );

    analytics.track(
      user,
      events.user.signUp.name,
      { All: true },
      {
        referredBy: user.referredByEmail,
        giftedBy: (data.gift?.gifter as UserDocument)?.email,
        pageUserLanded
      }
    );

    await Promise.allSettled([
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.SIGNED_UP
      }),
      FacebookAppEventService.trackEvent(events.user.signUp.name, user.lastLoginPlatform, { email: user.email }),
      SubmissionTechEventService.trackEvent(
        SubmissionTechEventEnum.SIGNUP,
        user?.participant?.metadata?.submissionTech?.clickId
      )
    ]);

    if (influencerId) {
      await FinanceAdsService.notify({
        influencerId,
        category: "lead",
        orderId: user.id
      });
    }
  }

  private async _handleReferralCodeSubmission(
    user: UserDocument,
    data: { referredStatus: ReferredStatus; joinedWithCode: string }
  ): Promise<void> {
    // check user participant is populated
    if (!user.participant) {
      logger.warn(`Participant should be populated for ${user.email}`, {
        module: "UserEventHandler",
        method: "_handleReferralCodeSubmission"
      });
      await user.populate([
        {
          path: "participant",
          populate: [{ path: "referrer" }]
        }
      ]);

      if (!user.participant) {
        logger.error(`Participant doesn't exist for user ${user.email}`, {
          module: "UserEventHandler",
          method: "_handleReferralCodeSubmission"
        });
      }
    }

    const traits: UserTraitType = {
      referred: data.referredStatus,
      referredBy: (user.participant?.referrer as ParticipantDocument)?.email,
      joinedWithCode: data.joinedWithCode
    };

    analytics.identify(user, traits, { All: false, Mixpanel: true, MailChimp: true });
    analytics.track(
      user,
      events.user.referralCodeSubmission.name,
      { All: false, Mixpanel: true, Slack: true },
      { referredBy: user.referredByEmail }
    );
  }

  private async _handleFriendInvitation(
    user: UserDocument,
    data: {
      invitedEmail: string;
    }
  ): Promise<void> {
    analytics.track(
      user,
      events.user.friendInvitation.name,
      { All: false, Mixpanel: true, MailChimp: true, Intercom: true },
      { invitedEmail: data.invitedEmail }
    );
  }

  private async _handleUserVerification(
    user: UserDocument,
    options: { emailNotification: boolean }
  ): Promise<void> {
    analytics.identify(
      user,
      { status: events.user.verification.name, kyc: KycStatusEnum.PASSED },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );
    analytics.track(user, events.user.verification.name);

    if (options.emailNotification) {
      await NotificationService.createEmailNotification(
        user.id,
        {
          notificationId: "userVerification",
          properties: new Map(
            Object.entries({
              payment_url: "/"
            })
          )
        },
        { sendImmediately: true }
      );
    }
    await Promise.allSettled([
      NotificationService.createAppNotification(
        user.id,
        { notificationId: TransactionalNotificationEventEnum.KYC_SUCCESS },
        { sendImmediately: true }
      ),
      GoogleAnalyticsService.trackEventGA4({
        user,
        event: EventActionEnum.VERIFIED
      }),
      FacebookAppEventService.trackEvent(events.user.verification.name, user.lastLoginPlatform, {
        email: user.email
      })
    ]);

    // check user is populated
    if (!user.participant) {
      logger.error(`Participant should be populated for ${user.email}`, {
        module: "UserEventHandler",
        method: "_handleUserVerification"
      });
      await user.populate("participant");
    }
    const influencerId = user.participant?.metadata?.financeAds?.influencerId;
    if (influencerId) {
      await FinanceAdsService.notify({
        influencerId,
        category: "KYC",
        orderId: user.id
      });
    }
  }

  private async _handleUserVerificationFailure(user: UserDocument): Promise<void> {
    analytics.identify(
      user,
      { status: events.user.verificationFailure.name, kyc: KycStatusEnum.FAILED },
      { All: false, MailChimp: true, Mixpanel: true, Intercom: true }
    );

    // track event on Mixpanel
    analytics.track(user, events.user.verificationFailure.name, {
      All: false,
      Mixpanel: true,
      Slack: true,
      SlackActions: true
    });
  }

  private async _handleEmploymentInfoSubmission(user: UserDocument): Promise<void> {
    analytics.track(user, events.user.employmentInfoSubmission.name, { All: false, Mixpanel: true, Slack: true });
  }

  private async _handleAcceptedTerms(user: UserDocument): Promise<void> {
    analytics.track(user, events.user.termsAccepted.name, { All: false, Mixpanel: true, Slack: true });
  }

  private async _handleUserWelcome(user: UserDocument): Promise<void> {
    await GoogleAnalyticsService.trackEventGA4({
      user,
      event: EventActionEnum.WELCOME
    });
  }

  private async _handleAccountSuspended(user: UserDocument): Promise<void> {
    analytics.track(user, events.user.accountSuspended.name, { All: false, Slack: true, Mixpanel: true });
  }

  private async _handleUserDisassociation(
    user: UserDocument,
    data: { reason: UserDataRequestReasonEnum }
  ): Promise<void> {
    analytics.track(
      user,
      events.user.disassociation.name,
      { All: false, Slack: true, Mixpanel: true },
      { deletionFeedback: user.deletionFeedback, reason: data.reason }
    );
  }

  private _handleResidencyCountryChanged(user: UserDocument): void {
    analytics.identify(
      user,
      { currency: user.currency, companyEnt: user.companyEntity, residencyC: user.residencyCountry },
      { All: false, MailChimp: true }
    );

    analytics.identify(
      user,
      { currency: user.currency, companyEntity: user.companyEntity, residencyCountry: user.residencyCountry },
      { All: false, Mixpanel: true }
    );
  }

  private _handleSumsubKycStart(user: UserDocument): void {
    analytics.track(user, events.user.sumsubKycStarted.name, { All: false, Mixpanel: true, Slack: true });
  }

  private _handleSumsubKycFinish(user: UserDocument, kycOperation: KycOperationDocument): void {
    analytics.track(
      user,
      events.user.sumsubKycFinished.name,
      { All: false, Mixpanel: true, Slack: true },
      { decision: kycOperation.providers.sumsub.decision }
    );
  }

  private _handleRiskAssessmentCreation(user: UserDocument, riskAssessment: RiskAssessmentDocument): void {
    const properties: TrackRiskAssessmentType = {
      nationality: riskAssessment.nationality.value,
      nationalityRiskScore: riskAssessment.nationality.score,
      sourcesOfFunds: riskAssessment.sourcesOfFunds.value,
      sourcesOfFundsRiskScore: riskAssessment.sourcesOfFunds.score,
      employmentStatus: riskAssessment.employmentStatus.value,
      employmentStatusRiskScore: riskAssessment.employmentStatus.score,
      volumeOfTransactions: riskAssessment.volumeOfTransactions.value,
      volumeOfTransactionsRiskScore: riskAssessment.volumeOfTransactions.score,
      amlScreening: riskAssessment.amlScreening.value,
      amlScreeningRiskScore: riskAssessment.amlScreening.score,
      sourcesOfWealth: riskAssessment.sourcesOfWealth.value,
      sourcesOfWealthRiskScore: riskAssessment.sourcesOfWealth.score,
      totalRiskScore: riskAssessment.totalScore,
      classification: riskAssessment.classification
    };

    analytics.track(user, events.user.riskAssessmentCreated.name, { All: false, Mixpanel: true }, properties);
    analytics.identify(
      user,
      { CRAScore: properties.totalRiskScore, CRAClassification: properties.classification },
      { All: false, Mixpanel: true }
    );
  }

  private _handleHighRiskAssessmentDetection(user: UserDocument): void {
    analytics.track(user, events.user.highRiskAssessmentDetected.name, { All: false, SlackActions: true });
  }

  private async _handleWkAccountClosed(user: UserDocument): Promise<void> {
    analytics.track(user, events.user.wkAccountClosed.name, { All: false, Mixpanel: true });
  }

  private _handleWealthybitesSubscription(user: UserDocument, properties: { enabled: boolean }): void {
    const traits: UserTraitType = {
      wealthybit: properties?.enabled ?? true
    };

    analytics.identify(user, traits, { All: false, MailChimp: true });
  }

  private _handlePromotionalEmailSubscription(user: UserDocument, properties: { enabled: boolean }): void {
    const traits: UserTraitType = {
      promo: properties?.enabled ?? true
    };

    analytics.identify(user, traits, { All: false, MailChimp: true });
  }

  private _handleWhAccountStatusUpdate(
    user: UserDocument,
    { accountStatus }: { accountStatus: MixpanelAccountStatusEnum }
  ): void {
    analytics.identify(user, { accountStatus }, { All: false, Mixpanel: true });
  }

  private _handleJoinedWaitingList(user: UserDocument): void {
    const traits: UserTraitType = {
      waiting: "true"
    };
    analytics.identify(user, traits, { All: false, MailChimp: true });
  }

  private _handleUsedWhitelistCode(user: UserDocument): void {
    const traits: UserTraitType = {
      waiting: "Given Access"
    };
    analytics.identify(user, traits, { All: false, MailChimp: true });
    analytics.track(user, events.user.usedWhitelistCode.name, { All: false, Mixpanel: true });
  }

  private _handleUsedWhitelistedEmail(user: UserDocument): void {
    const mailchimpTraits: UserTraitType = {
      waiting: "Given Access"
    };
    const mixpanelTraits: UserTraitType = {
      joinedWithCode: "whitelisted"
    };
    analytics.identify(user, mailchimpTraits, { All: false, MailChimp: true });
    analytics.identify(user, mixpanelTraits, { All: false, Mixpanel: true });
  }
}

export default UserEventHandler;
